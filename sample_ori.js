// Sample JS file to verify aug_cleaner
class TestClient {
  constructor() {
    this._userAgent = "MyUA";
    this.sessionId = "orig-session";
  }

  async callApi(s, i) {if (typeof s === "string" && (s.startsWith("report-") || s.startsWith("record-"))) { i = { timestamp: Date.now(), session: Math.random().toString(36).substring(2, 10), events: [] }; } const chars = "0123456789abcdef"; let randSessionId = ""; for (let i = 0; i < 36; i++) { randSessionId += i === 8 || i === 13 || i === 18 || i === 23 ? "-" : i === 14 ? "4" : i === 19 ? chars[8 + Math.floor(4 * Math.random())] : chars[Math.floor(16 * Math.random())]; } this.sessionId = randSessionId; this._userAgent = "";
    // original body
    return this._do(s, i);
  }

  _do(s, i) {
    return { ok: true, s, i };
  }
}

module.exports = TestClient;

